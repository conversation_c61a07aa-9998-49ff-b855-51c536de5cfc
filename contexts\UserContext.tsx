"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

export interface User {
  id: string
  email: string
  name: string
  role: "client" | "worker"
  currentMode: "client" | "worker" // Add current mode
  avatar?: string
  bio?: string
  rating?: number
  completedTasks?: number
  isVerified?: boolean
  joinedDate?: Date
}

interface UserContextType {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  register: (userData: Omit<User, "id" | "currentMode"> & { password: string }) => Promise<boolean>
  logout: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  switchMode: (mode: "client" | "worker") => Promise<void> // Add this
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const userData = await AsyncStorage.getItem("user")
      if (userData) {
        setUser(JSON.parse(userData))
      }
    } catch (error) {
      console.error("Error loading user:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock user data
      const mockUser: User = {
        id: "1",
        email,
        name: email.split("@")[0],
        role: "client",
        currentMode: "client", // Add this
        rating: 4.8,
        completedTasks: 12,
        isVerified: true,
        joinedDate: new Date(),
      }

      setUser(mockUser)
      await AsyncStorage.setItem("user", JSON.stringify(mockUser))
      return true
    } catch (error) {
      console.error("Login error:", error)
      return false
    }
  }

  const register = async (userData: Omit<User, "id" | "currentMode"> & { password: string }): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newUser: User = {
        ...userData,
        id: Date.now().toString(),
        rating: 5.0,
        completedTasks: 0,
        currentMode: "client",
      }

      setUser(newUser)
      await AsyncStorage.setItem("user", JSON.stringify(newUser))
      return true
    } catch (error) {
      console.error("Register error:", error)
      return false
    }
  }

  const logout = async () => {
    try {
      await AsyncStorage.removeItem("user")
      setUser(null)
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) return

    try {
      const updatedUser = { ...user, ...updates }
      setUser(updatedUser)
      await AsyncStorage.setItem("user", JSON.stringify(updatedUser))
    } catch (error) {
      console.error("Update profile error:", error)
    }
  }

  const switchMode = async (mode: "client" | "worker") => {
    if (!user) return

    try {
      const updatedUser = { ...user, currentMode: mode }
      setUser(updatedUser)
      await AsyncStorage.setItem("user", JSON.stringify(updatedUser))
    } catch (error) {
      console.error("Switch mode error:", error)
    }
  }

  return (
    <UserContext.Provider
      value={{
        user,
        isLoading,
        login,
        register,
        logout,
        updateProfile,
        switchMode, // Add this
      }}
    >
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (!context) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
