"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import AsyncStorage from "@react-native-async-storage/async-storage"

type Theme = "light" | "dark"

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  colors: {
    primary: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    error: string
    accent: string
  }
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

// Update the color schemes to be more professional
const lightColors = {
  primary: "#4A90E2", // Soft professional blue
  secondary: "#6C7B7F", // Professional gray-blue
  background: "#FFFFFF", // Pure white
  surface: "#F8F9FA", // Very light gray
  text: "#2C3E50", // Dark professional gray
  textSecondary: "#6C757D", // Medium gray
  border: "#E9ECEF", // Light border gray
  success: "#28A745", // Professional green
  warning: "#FFC107", // Professional amber
  error: "#DC3545", // Professional red
  accent: "#17A2B8", // Professional teal
}

const darkColors = {
  primary: "#5BA0F2", // Slightly lighter blue for dark mode
  secondary: "#8A9BA8", // Lighter gray-blue for dark mode
  background: "#1A1D23", // Professional dark background
  surface: "#2D3748", // Dark surface
  text: "#F7FAFC", // Light text
  textSecondary: "#A0AEC0", // Medium light gray
  border: "#4A5568", // Dark border
  success: "#48BB78", // Darker green
  warning: "#ED8936", // Darker amber
  error: "#F56565", // Darker red
  accent: "#4FD1C7", // Darker teal
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>("light")

  useEffect(() => {
    loadTheme()
  }, [])

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem("theme")
      if (savedTheme) {
        setTheme(savedTheme as Theme)
      }
    } catch (error) {
      console.error("Error loading theme:", error)
    }
  }

  const toggleTheme = async () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    try {
      await AsyncStorage.setItem("theme", newTheme)
    } catch (error) {
      console.error("Error saving theme:", error)
    }
  }

  const colors = theme === "light" ? lightColors : darkColors

  return <ThemeContext.Provider value={{ theme, toggleTheme, colors }}>{children}</ThemeContext.Provider>
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider")
  }
  return context
}
